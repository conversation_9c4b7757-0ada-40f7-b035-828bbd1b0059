import type pino from "pino";

import type { LLM_Providers } from "../providers";
import type { keys } from "@/shared/database/schema/keys";
import type { KeyCheckerBase } from "./base-checker";

export type Key = (typeof keys)["$inferSelect"];

/**
 * KeyPool and KeyProvider's similarities are a relic of the old design where
 * there was only a single KeyPool for OpenAI keys. Now that there are multiple
 * supported services, the service-specific functionality has been moved to
 * KeyProvider and KeyPool is just a wrapper around multiple KeyProviders,
 * delegating to the appropriate one based on the model requested.
 *
 * Existing code will continue to call methods on KeyPool, which routes them to
 * the appropriate KeyProvider or returns data aggregated across all KeyProviders
 * for service-agnostic functionality.
 */

export interface KeyProvider<T extends Key = Key> {
  /** The service this key provider is for. */
  readonly provider: LLM_Providers;

  /** The logger for this key provider. */
  readonly logger: pino.Logger;

  /** Initialize the key provider. */
  init({ checker }: { checker?: KeyCheckerBase<T> }): Promise<void>;

  /**
   * Add a key to the pool.
   * @returns Whether the key was added. If false, the key was already present.
   */
  addKey(key: string): Promise<boolean>;

  /**
   * Remove a key from the pool. This is used when a key is revoked or otherwise
   * should not be used. The key can be re-added by calling `addKey`.
   * @returns Whether the key was removed. If false, the key was not found.
   */
  removeKey(hash: string): Promise<boolean>;

  /**
   * Get a key for a given model.
   * @param model The model to get a key for.
   */
  get(model: string): Promise<T>;

  /**
   * List all keys in the pool.
   * @returns A list of all keys in the pool.
   */
  list(): Promise<T[]>;

  /**
   * Update a key's properties in the pool.
   * @param hash The hash of the key to update.
   * @param update The properties to update.
   */
  update(hash: string, update: Partial<T>): Promise<void>;

  /**
   * Get the number of available keys for a given model.
   * @param modelId The model to check availability for.
   * @param modelId If not provided, check availability for all models.
   * @return The number of available keys.
   */
  available(modelId?: string): Promise<number>;

  /**
   * Increment the usage of a key.
   * @param data The usage data to increment.
   */
  incrementUsage(
    hash: string,
    data: {
      modelFamily: string;
      inputTokens: number;
      outputTokens: number;
      reasoningTokens?: number;
    },
  ): Promise<void>;

  /**
   * Mark a key as rate limited. This will disable the key and set a timer to
   * re-enable it after the rate limit has passed.
   *
   * @param hash The hash of the key to mark as rate limited.
   */
  markRateLimited(hash: string): Promise<void>;

  /**
   * Recheck all keys in the pool. This will check the status of each key and
   * re-enable any that are disabled.
   */
  recheck(): Promise<void>;
}

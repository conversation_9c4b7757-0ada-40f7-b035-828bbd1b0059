import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type PaginationState,
  type SortingState,
  type VisibilityState,
} from "@tanstack/react-table";

import { CheckCircleIcon, KeyIcon, PlusIcon, RefreshCwIcon, SearchIcon } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { NavLink } from "react-router";
import { toast } from "sonner";

import { DataTable } from "@/components/table";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Icons } from "@/components/ui/icons";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { AddKeyDialog } from "@/components/admin/add-key-dialog";
import { KeyActions } from "@/components/admin/key-actions";
import { KeyStatus } from "@/components/admin/key-status";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { DataTableViewOptions } from "@/components/table/data-table-view-options";

import type { Key } from "@/shared/key-management";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";

import { useTRPC } from "@/lib/trpc/client";
import { format } from "@/shared/utils";

type KeyStatus = Key["status"] | "all";

const columns: ColumnDef<Key>[] = [
  {
    accessorKey: "hash",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Key ID" />,
    cell: ({ getValue }) => {
      return (
        <NavLink to={`/admin/keys/${getValue()}`} className="font-mono text-sm">
          <span className="underline-offset-4 hover:underline">
            {getValue<string>().slice(0, 12)}
          </span>
          ...
        </NavLink>
      );
    },
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: "provider",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Provider" />,
    cell: ({ getValue }) => {
      const provider = getValue<LLM_Providers>();

      return (
        <div className="flex items-center gap-2">
          <Icons.provider provider={provider} className="size-4" />
          <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
        </div>
      );
    },
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      return <KeyStatus keyData={row.original} />;
    },
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "modelIds",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Models" />,
    cell: ({ getValue }) => {
      const modelIds = getValue() as string[];
      return (
        <div className="text-muted-foreground text-sm">
          {modelIds.length > 0 ? `${modelIds.length} models` : "No models"}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "lastUsedAt",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Last Used" />,
    cell: ({ getValue }) => {
      const timestamp = getValue() as number;
      return (
        <div className="text-muted-foreground text-sm">
          {timestamp > 0 ? format.date(timestamp) : "Never"}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Created" />,
    cell: ({ getValue }) => (
      <div className="text-muted-foreground text-sm">{format.date(getValue<number>() * 1000)}</div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "lastCheckedAt",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Last Checked" />,
    cell: ({ getValue }) => {
      const timestamp = getValue() as number;
      return (
        <div className="text-muted-foreground text-sm">
          {timestamp > 0 ? format.date(timestamp) : "None"}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "nextCheckAt",
    header: ({ column }) => <DataTableColumnHeader column={column} title="Next Check" />,
    cell: ({ getValue }) => {
      const timestamp = getValue() as number;
      return (
        <div className="text-muted-foreground text-sm">
          {timestamp > 0 ? format.date(timestamp) : "None"}
        </div>
      );
    },
    enableSorting: true,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => <KeyActions keyData={row.original} />,
    enableHiding: false,
  },
];

export function AdminKeysPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<LLM_Providers | "all">("all");
  const [selectedStatus, setSelectedStatus] = useState<KeyStatus>("all");
  const [isAddKeyDialogOpen, setIsAddKeyDialogOpen] = useState(false);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { data: keys, isLoading } = useQuery(trpc.keys.list.queryOptions({}));

  // Mutations for refresh and recheck
  const { mutate: recheckProvider, isPending: isRecheckingProvider } = useMutation({
    ...trpc.keys.recheck.mutationOptions(),
    onSuccess: () => {
      toast.success("Key recheck started successfully");
      queryClient.invalidateQueries({ queryKey: ["keys"] });
    },
    onError: (error) => {
      toast.error(`Failed to start recheck: ${error.message}`);
    },
  });

  const { mutate: recheckAll, isPending: isRecheckingAll } = useMutation({
    ...trpc.keys.recheckAll.mutationOptions(),
    onSuccess: () => {
      toast.success("Recheck started for all providers");
      queryClient.invalidateQueries({ queryKey: ["keys"] });
    },
    onError: (error) => {
      toast.error(`Failed to start recheck: ${error.message}`);
    },
  });

  // Type the keys data properly - stable reference
  const typedKeys = useMemo(() => (keys as Key[]) || [], [keys]);

  // Filter keys based on search and filters - memoized for performance
  const filteredKeys = useMemo(() => {
    return typedKeys.filter((key) => {
      const matchesSearch = key.hash.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesProvider = selectedProvider === "all" || key.provider === selectedProvider;
      const matchesStatus = selectedStatus === "all" || key.status === selectedStatus;

      return matchesSearch && matchesProvider && matchesStatus;
    });
  }, [typedKeys, searchQuery, selectedProvider, selectedStatus]);

  // Statistics - memoized for performance
  const { totalKeys, workingKeys, revokedKeys } = useMemo(() => {
    const total = typedKeys.length;
    const working = typedKeys.filter((k) => k.status === "working").length;
    const revoked = typedKeys.filter((k) => k.status === "revoked").length;

    return { totalKeys: total, workingKeys: working, revokedKeys: revoked };
  }, [typedKeys]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleProviderChange = useCallback((value: LLM_Providers | "all") => {
    setSelectedProvider(value);
  }, []);

  const handleStatusChange = useCallback((value: KeyStatus) => {
    setSelectedStatus(value);
  }, []);

  const handleClearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedProvider("all");
    setSelectedStatus("all");
  }, []);

  const handleRefresh = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ["keys"] });
    toast.success("Keys refreshed");
  }, [queryClient]);

  const handleRecheck = useCallback(() => {
    if (selectedProvider !== "all") {
      recheckProvider({ provider: selectedProvider });
    } else {
      recheckAll();
    }
  }, [selectedProvider, recheckProvider, recheckAll]);

  // Optimized table configuration with stable references
  const table = useReactTable({
    data: filteredKeys,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    state: { sorting, columnVisibility, pagination },

    // Performance optimizations
    enableRowSelection: false,
    enableMultiRowSelection: false,
    manualFiltering: false,
    manualSorting: false,
    manualPagination: false,
  });

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Total Keys</CardTitle>
            <KeyIcon className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalKeys}</div>
            <p className="text-muted-foreground text-xs">Across all providers</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Working Keys</CardTitle>
            <div className="h-4 w-4 rounded-full bg-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{workingKeys}</div>
            <p className="text-muted-foreground text-xs">Available for use</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Revoked Keys</CardTitle>
            <div className="h-4 w-4 rounded-full bg-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{revokedKeys}</div>
            <p className="text-muted-foreground text-xs">Need attention</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Usage Rate</CardTitle>
            <div className="h-4 w-4 rounded-full bg-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalKeys > 0 ? Math.round((workingKeys / totalKeys) * 100) : 0}%
            </div>
            <p className="text-muted-foreground text-xs">Keys working</p>
          </CardContent>
        </Card>
      </div>

      {/* Filter Bar */}
      <div className="flex flex-col items-center gap-2">
        <div className="relative w-full flex-1">
          <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
          <Input
            placeholder="Search keys by ID..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>

        <div className="flex w-full items-center justify-between gap-2">
          <div className="flex gap-2">
            {/* Provider Filter */}
            <Select value={selectedProvider} onValueChange={handleProviderChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Provider" />
              </SelectTrigger>

              <SelectContent>
                <SelectItem value="all">All Providers</SelectItem>

                {LLM_PROVIDERS.map((provider) => (
                  <SelectItem key={provider} value={provider}>
                    <div className="flex items-center gap-2">
                      <Icons.provider provider={provider} className="size-4" />
                      <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>

              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="working">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500" />
                    <span>Working</span>
                  </div>
                </SelectItem>
                <SelectItem value="out_of_quota">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-orange-500" />
                    <span>Out of Quota</span>
                  </div>
                </SelectItem>
                <SelectItem value="revoked">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-red-500" />
                    <span>Revoked</span>
                  </div>
                </SelectItem>
                <SelectItem value="ratelimited">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-yellow-500" />
                    <span>Rate Limited</span>
                  </div>
                </SelectItem>
                <SelectItem value="disabled">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-gray-500" />
                    <span>Disabled</span>
                  </div>
                </SelectItem>
                <SelectItem value="unknown">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-gray-400" />
                    <span>Unknown</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <Button
              data-show={
                searchQuery.length > 0 || selectedProvider !== "all" || selectedStatus !== "all"
              }
              onClick={handleClearFilters}
              className="hidden data-[show=true]:block"
            >
              Clear
            </Button>
          </div>

          <div className="flex items-center gap-2">
            {/* Add Key Button */}
            <Button onClick={() => setIsAddKeyDialogOpen(true)}>
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Key
            </Button>

            {/* Refresh Button */}
            <Button variant="outline" size="icon" onClick={handleRefresh} title="Refresh">
              <RefreshCwIcon className="h-4 w-4" />
            </Button>

            {/* Recheck Button */}
            <Button
              variant="outline"
              onClick={handleRecheck}
              disabled={isRecheckingProvider || isRecheckingAll}
              title={
                selectedProvider !== "all"
                  ? `Recheck ${LLM_PROVIDER_DISPLAY_NAME[selectedProvider]} keys`
                  : "Recheck all keys"
              }
            >
              <CheckCircleIcon className="mr-2 h-4 w-4" />
              {isRecheckingProvider || isRecheckingAll ? "Rechecking..." : "Recheck"}
            </Button>

            <DataTableViewOptions table={table} />
          </div>
        </div>
      </div>

      {/* Keys Table */}
      {isLoading ? (
        <div className="flex h-24 items-center justify-center">
          <div className="text-muted-foreground">Loading API keys...</div>
        </div>
      ) : (
        <DataTable table={table} />
      )}

      {/* Add Key Dialog */}
      <AddKeyDialog open={isAddKeyDialogOpen} onOpenChange={setIsAddKeyDialogOpen} />
    </div>
  );
}

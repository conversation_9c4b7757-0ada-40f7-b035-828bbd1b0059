import schedule from "node-schedule";

import { config } from "@/config";
import { logger } from "@/logger";

import { DeepseekKeyProvider } from "./providers/deepseek/provider";
import { GeminiKeyProvider } from "./providers/gemini/provider";
import { OpenAIKeyProvider } from "./providers/openai/provider";

import type { LLM_Providers } from "../providers";
import type { Key, KeyProvider } from "./index";

export class KeysPool {
  private keyProviders: KeyProvider[] = [];
  public registeredProviders: LLM_Providers[] = [];

  private recheckJobs?: schedule.Job;

  private static instance: KeysPool;

  private constructor() {}

  /**
   * Register a key provider. This should only be called once per provider.
   */
  private registerKeyProvider(keyProvider: KeyProvider) {
    if (this.registeredProviders.includes(keyProvider.provider)) {
      throw new Error(`Keys Provider ${keyProvider.provider} already registered`);
    }

    this.keyProviders.push(keyProvider);
    this.registeredProviders.push(keyProvider.provider);
  }

  /**
   * Create a singleton key pool. This should only be called once.
   */
  public static createKeyPool(): KeysPool {
    const instance = (KeysPool.instance ??= new KeysPool());

    instance.registerKeyProvider(new OpenAIKeyProvider());
    instance.registerKeyProvider(new GeminiKeyProvider());
    instance.registerKeyProvider(new DeepseekKeyProvider());

    return instance;
  }

  /**
   * Initialize the key pool. This will load all keys from the database and
   * start the key checker for each provider.
   */
  public async init() {
    await Promise.all(this.keyProviders.map((provider) => provider.init({})));
    this.scheduleRecheck();
  }

  /**
   * Get a key for a given model.
   * @param model The model to get a key for.
   * @param provider The provider to get a key for.
   */
  public async get(model: string, provider: LLM_Providers): Promise<Key> {
    return this.getKeyProvider(provider).get(model);
  }

  /**
   * List all keys in the pool.
   * @returns A list of all keys in the pool.
   */
  public async list() {
    const keys = await Promise.all(this.keyProviders.map((provider) => provider.list()));
    return keys.flat();
  }

  /**
   * Add keys to the pool. This is used when a key is first added or when a
   * key is re-added after being removed.
   * @param provider The provider to add keys to.
   * @param keys The keys to add.
   * @return The number of keys added.
   */
  public async addKeys(provider: LLM_Providers, keys: string[]) {
    let successCount = 0;
    for (const key of keys) {
      const success = await this.getKeyProvider(provider).addKey(key);
      if (success) successCount++;
    }

    return successCount;
  }

  /**
   * Remove keys from the pool. This is used when a key is revoked or otherwise
   * should not be used. The key can be re-added by calling `addKey`.
   * @param provider The provider to remove keys from.
   * @param hashes The hashes of the keys to remove.
   * @return The number of keys removed.
   */
  public async removeKeys(provider: LLM_Providers, hashes: string[]) {
    let successCount = 0;
    for (const hash of hashes) {
      const success = await this.getKeyProvider(provider).removeKey(hash);
      if (success) successCount++;
    }

    return successCount;
  }

  /**
   * Update a key's properties in the pool.
   * @param key The key to update.
   * @param props The properties to update.
   */
  public async update(key: Key, props: Partial<Key>) {
    return await this.getKeyProvider(key.provider).update(key.hash, props);
  }

  /**
   * Get the number of available keys for a given model.
   * @param modelId The model to check availability for.
   * @param modelId If not provided, check availability for all models.
   * @return The number of available keys.
   */
  public async available(modelId?: string): Promise<number> {
    const counts = await Promise.all(
      this.keyProviders.map(async (provider) => await provider.available(modelId)),
    );

    return counts.reduce((acc, count) => acc + count, 0);
  }

  // public async incrementUsage(key: Key, model: string, inputTokens: number, outputTokens: number): void {}

  // public getLockoutPeriod(family: ModelFamily): number {}

  public async markRateLimited(key: Key) {}

  public async updateRateLimits(key: Key, headers: Headers) {
    throw new Error("Not implemented");
  }

  public async recheck(provider: LLM_Providers) {
    if (!config.checkKeys) {
      logger.info("Skipping key recheck because key checking is disabled");
      return;
    }

    await this.getKeyProvider(provider).recheck();
  }

  private getServiceForModel(model: string): LLM_Providers {
    throw new Error("Not implemented");
  }

  public getKeyProvider<T extends KeyProvider>(provider: LLM_Providers): T {
    return this.keyProviders.find((keyProvider) => keyProvider.provider === provider) as T;
  }

  /**
   * Schedules a periodic recheck of all providers, which run every 6 hours.
   * It doesn't follow machine timezone, instead it runs in UTC.
   */
  private scheduleRecheck() {
    this.recheckJobs?.cancel();
    this.recheckJobs = undefined;

    // Run every 6 hours at minute 0, second 0 in UTC: 00:00, 06:00, 12:00, 18:00
    // node-schedule uses the server's clock but we can express a "*/6" hour schedule,
    // which is independent of local timezone wall clock for cadence purposes.
    const rule = new schedule.RecurrenceRule();

    rule.hour = new schedule.Range(0, 23, 6);
    rule.minute = 0;
    rule.tz = "Etc/UTC";

    this.recheckJobs = schedule.scheduleJob("key-checker:recheck:all", rule, async () => {
      logger.info("Scheduling periodic key recheck...");
      await Promise.allSettled(this.keyProviders.map((provider) => provider.recheck()));
      logger.info("Scheduled periodic key recheck.");
    });
  }
}

export const keysPool = KeysPool.createKeyPool();

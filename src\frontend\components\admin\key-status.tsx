import { useMemo } from "react";
import { ClockIcon } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import type { Key } from "@/shared/key-management";
import { format } from "@/shared/utils";

interface KeyStatusProps {
  keyData: Key;
}

// Status configuration
const statusConfig = {
  working: { label: "Working", dotColor: "bg-green-500" },
  out_of_quota: { label: "Out of Quota", dotColor: "bg-orange-500" },
  revoked: { label: "Revoked", dotColor: "bg-red-500" },
  ratelimited: { label: "Rate Limited", dotColor: "bg-yellow-500" },
  disabled: { label: "Disabled", dotColor: "bg-gray-500" },
  unknown: { label: "Unknown", dotColor: "bg-gray-400" },
};

function StatusDot({ status }: { status: Key["status"] }) {
  const config = statusConfig[status];
  return (
    <div className="flex items-center gap-2">
      <div className={`h-2 w-2 rounded-full ${config.dotColor}`} />
      <span className="text-sm">{config.label}</span>
    </div>
  );
}

export function KeyStatus({ keyData }: KeyStatusProps) {
  const pendingDeletion = keyData.metadata?.pendingDeletion as number | undefined;
  const disableReason = keyData.metadata?.disableReason as string | undefined;
  
  const deletionInfo = useMemo(() => {
    if (!pendingDeletion) return null;
    
    const deletionTime = pendingDeletion * 1000; // Convert to milliseconds
    const now = Date.now();
    const timeRemaining = deletionTime - now;
    
    if (timeRemaining <= 0) {
      return { expired: true, timeText: "Deletion overdue" };
    }
    
    const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
    
    let timeText = "";
    if (hours > 0) {
      timeText = `${hours}h ${minutes}m`;
    } else {
      timeText = `${minutes}m`;
    }
    
    return {
      expired: false,
      timeText: `Deletes in ${timeText}`,
      deletionDate: format.date(deletionTime),
    };
  }, [pendingDeletion]);

  return (
    <div className="flex items-center gap-2">
      <StatusDot status={keyData.status} />
      
      {/* Pending Deletion Indicator */}
      {deletionInfo && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge 
                variant="destructive" 
                className="flex items-center gap-1 text-xs"
              >
                <ClockIcon className="h-3 w-3" />
                {deletionInfo.expired ? "Overdue" : "Pending"}
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <p className="font-medium">{deletionInfo.timeText}</p>
                {deletionInfo.deletionDate && (
                  <p className="text-xs text-muted-foreground">
                    Scheduled: {deletionInfo.deletionDate}
                  </p>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
      
      {/* Disable Reason Tooltip */}
      {disableReason && keyData.status === "disabled" && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="text-xs">
                Info
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="max-w-xs">
                <p className="font-medium">Disable Reason:</p>
                <p className="text-sm text-muted-foreground">{disableReason}</p>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}
